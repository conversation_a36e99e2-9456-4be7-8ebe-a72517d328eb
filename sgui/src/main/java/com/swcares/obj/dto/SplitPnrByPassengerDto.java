package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 旅客分离请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(value = "SplitPnrByPassengerDto", description = "旅客分离请求DTO")
public class SplitPnrByPassengerDto {

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "计数")
    private Integer count;

    @ApiModelProperty(value = "PNR编号")
    private String passengerRecordLocator;

    @ApiModelProperty(value = "要分离的旅客列表")
    private List<Traveller> travellers;

    @ApiModelProperty(value = "是否团队")
    private Boolean isGrp;

    @Data
    @ApiModel(value = "Traveller", description = "旅客信息")
    public static class Traveller {
        @ApiModelProperty(value = "旅客ID（对应psgIndex）")
        private Integer paxId;

        @ApiModelProperty(value = "旅客姓名")
        private String fullName;

        @ApiModelProperty(value = "无陪儿童")
        private Boolean unMinor;

        @ApiModelProperty(value = "无陪儿童年龄")
        private Integer unMinorAge;
    }
}
