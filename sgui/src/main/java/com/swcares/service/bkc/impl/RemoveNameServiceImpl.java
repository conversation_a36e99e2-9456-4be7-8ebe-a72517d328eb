package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.*;
import com.swcares.obj.dto.RemoveNameDto;
import com.swcares.service.*;
import com.swcares.service.bkc.IRemoveNameService;
import com.swcares.service.bkc.IUpdatePnrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 删除旅客服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/15 10:30
 */
@Slf4j
@Service
public class RemoveNameServiceImpl implements IRemoveNameService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxNmTcService iMnjxNmTcService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IUpdatePnrService iUpdatePnrService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeName(RemoveNameDto dto) throws SguiResultException {
        // 1. 根据PNR查询PNR信息
        MnjxPnr pnr = this.getPnrByPnrNo(dto.getPnr());

        // 2. 验证删除条件
        this.validateRemoveConditions(pnr, dto);

        // 3. 收集要删除的数据
        List<Object> deleteList = new ArrayList<>();
        List<Object> updateList = new ArrayList<>();

        // 4. 处理旅客删除
        if (CollUtil.isNotEmpty(dto.getTravellers())) {
            this.processTravellerRemoval(pnr, dto.getTravellers(), deleteList);
            // 删除了多少旅客，就需要将seg的座位数更新减少多少
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                    .ne(MnjxPnrSeg::getPnrSegType, "SA")
                    .list();
            pnrSegList.forEach(p -> {
                p.setSeatNumber(p.getSeatNumber() - dto.getTravellers().size());
                String inputValue = p.getInputValue();
                if (p.getInputValue().contains(" KHK")) {
                    inputValue = p.getInputValue().replaceAll("HK\\d+", StrUtil.format("HK{}", p.getSeatNumber()));
                } else if (p.getInputValue().contains(" RR")) {
                    inputValue = p.getInputValue().replaceAll("RR\\d+", StrUtil.format("RR{}", p.getSeatNumber()));
                } else if (p.getInputValue().contains(" DK")) {
                    inputValue = p.getInputValue().replaceAll("DK\\d+", StrUtil.format("DK{}", p.getSeatNumber()));
                } else if (p.getInputValue().contains(" NN")) {
                    inputValue = p.getInputValue().replaceAll("NN\\d+", StrUtil.format("NN{}", p.getSeatNumber()));
                }
                p.setInputValue(inputValue);
            });
            updateList.addAll(pnrSegList);
        }

        // 5. 处理婴儿删除
        if (CollUtil.isNotEmpty(dto.getInfants())) {
            this.processInfantRemoval(pnr, dto.getInfants(), deleteList, updateList);
        }

        // 6. 处理PNR级别运价删除
        this.processPnrLevelFareRemoval(pnr, deleteList);

        // 更新相关的PNR记录，设置changeMark为X
        List<MnjxPnrRecord> dbRecordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .list();
        this.updateRelatedPnrRecords(dbRecordList, deleteList, updateList);

        // 7. 生成新的封口编号并设置changeAtNo
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());
        this.setChangeAtNoForUpdates(updateList, newAtNo);

        // 8. 批量执行删除和更新操作
        this.batchExecuteOperations(deleteList, updateList);

        // 9. 重新排序和生成封口记录
        this.reorderAndSeal(pnr, dto.getEnvelopType(), newAtNo);
    }

    /**
     * 根据PNR编号查询PNR信息
     */
    private MnjxPnr getPnrByPnrNo(String pnrNo) throws SguiResultException {
        if (StrUtil.isEmpty(pnrNo)) {
            throw new SguiResultException("PNR编号不能为空");
        }

        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, pnrNo)
                .one();

        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息：" + pnrNo);
        }

        return pnr;
    }

    /**
     * 验证删除条件
     */
    private void validateRemoveConditions(MnjxPnr pnr, RemoveNameDto dto) throws SguiResultException {
        // 查询所有非婴儿旅客
        List<MnjxPnrNm> allPassengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();

        if (CollUtil.isEmpty(allPassengers)) {
            throw new SguiResultException("PNR中没有旅客信息");
        }

        // 如果要删除旅客，检查是否删除最后一个非婴儿旅客
        if (CollUtil.isNotEmpty(dto.getTravellers())) {
            List<Integer> toDeletePaxIds = dto.getTravellers().stream()
                    .map(RemoveNameDto.Traveller::getPaxId)
                    .collect(Collectors.toList());

            long remainingPassengers = allPassengers.stream()
                    .filter(p -> !toDeletePaxIds.contains(p.getPsgIndex()))
                    .count();

            if (remainingPassengers == 0) {
                throw new SguiResultException("USE XE PNR");
            }
        }
    }

    /**
     * 处理旅客删除
     */
    private void processTravellerRemoval(MnjxPnr pnr, List<RemoveNameDto.Traveller> travellers, List<Object> deleteList) throws SguiResultException {
        for (RemoveNameDto.Traveller traveller : travellers) {
            // 查询旅客信息
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, traveller.getPaxId())
                    .one();

            if (pnrNm == null) {
                log.warn("未找到旅客信息，psgIndex: {}", traveller.getPaxId());
                continue;
            }

            // 删除旅客主记录
            deleteList.add(pnrNm);

            // 删除旅客相关的所有数据
            this.deletePassengerRelatedData(pnrNm.getPnrNmId(), deleteList);
        }
    }

    /**
     * 删除旅客相关数据
     */
    private void deletePassengerRelatedData(String pnrNmId, List<Object> deleteList) {
        // 删除SSR记录（排除婴儿相关的SSR INFT）
        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .ne(MnjxNmSsr::getSsrType, "INFT")
                .list();
        deleteList.addAll(ssrList);

        // 删除CT记录
        List<MnjxNmCt> ctList = iMnjxNmCtService.lambdaQuery()
                .eq(MnjxNmCt::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(ctList);

        // 删除OSI记录
        List<MnjxNmOsi> osiList = iMnjxNmOsiService.lambdaQuery()
                .eq(MnjxNmOsi::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(osiList);

        // 删除RMK记录
        List<MnjxNmRmk> rmkList = iMnjxNmRmkService.lambdaQuery()
                .eq(MnjxNmRmk::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(rmkList);

        // 删除运价记录（排除婴儿运价）
        List<MnjxNmFp> fpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrNmId, pnrNmId)
                .ne(MnjxNmFp::getIsBaby, 1)
                .list();
        deleteList.addAll(fpList);

        List<MnjxNmFc> fcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrNmId, pnrNmId)
                .ne(MnjxNmFc::getIsBaby, 1)
                .list();
        deleteList.addAll(fcList);

        List<MnjxNmFn> fnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .ne(MnjxNmFn::getIsBaby, 1)
                .list();
        deleteList.addAll(fnList);

        // 删除EI记录
        List<MnjxNmEi> eiList = iMnjxNmEiService.lambdaQuery()
                .eq(MnjxNmEi::getPnrNmId, pnrNmId)
                .notLike(MnjxNmEi::getInputValue, "/IN/")
                .list();
        deleteList.addAll(eiList);

        // 删除TC记录
        List<MnjxNmTc> tcList = iMnjxNmTcService.lambdaQuery()
                .eq(MnjxNmTc::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(tcList);

        // 删除OI记录
        List<MnjxNmOi> oiList = iMnjxNmOiService.lambdaQuery()
                .eq(MnjxNmOi::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(oiList);
    }

    /**
     * 处理婴儿删除
     */
    private void processInfantRemoval(MnjxPnr pnr, List<RemoveNameDto.Infant> infants, List<Object> deleteList, List<Object> updateList) throws SguiResultException {
        for (RemoveNameDto.Infant infant : infants) {
            // 解析成人旅客索引
            String adultIndexStr = infant.getAdaultIndex();
            if (StrUtil.isEmpty(adultIndexStr) || !adultIndexStr.startsWith("P")) {
                log.warn("无效的成人旅客索引: {}", adultIndexStr);
                continue;
            }

            Integer adultPsgIndex = Integer.parseInt(adultIndexStr.substring(1));

            // 查询成人旅客信息
            MnjxPnrNm adultPnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, adultPsgIndex)
                    .one();

            if (adultPnrNm == null) {
                log.warn("未找到成人旅客信息，psgIndex: {}", adultPsgIndex);
                continue;
            }

            // 查询婴儿信息
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, adultPnrNm.getPnrNmId())
                    .one();

            if (nmXn == null) {
                log.warn("未找到婴儿信息，成人pnrNmId: {}", adultPnrNm.getPnrNmId());
                continue;
            }

            // 删除婴儿记录
            deleteList.add(nmXn);

            // 修改成人旅客类型
            adultPnrNm.setPsgType("0");
            updateList.add(adultPnrNm);

            // 删除婴儿相关数据
            this.deleteInfantRelatedData(adultPnrNm.getPnrNmId(), deleteList);
        }
    }

    /**
     * 删除婴儿相关数据
     */
    private void deleteInfantRelatedData(String pnrNmId, List<Object> deleteList) {
        // 删除SSR INFT记录
        List<MnjxNmSsr> infantSsrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .eq(MnjxNmSsr::getSsrType, "INFT")
                .list();
        deleteList.addAll(infantSsrList);

        // 删除婴儿运价记录
        List<MnjxNmFp> infantFpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrNmId, pnrNmId)
                .eq(MnjxNmFp::getIsBaby, 1)
                .list();
        deleteList.addAll(infantFpList);

        List<MnjxNmFc> infantFcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrNmId, pnrNmId)
                .eq(MnjxNmFc::getIsBaby, 1)
                .list();
        deleteList.addAll(infantFcList);

        List<MnjxNmFn> infantFnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, 1)
                .list();
        deleteList.addAll(infantFnList);

        // 删除婴儿EI记录
        List<MnjxNmEi> infantEiList = iMnjxNmEiService.lambdaQuery()
                .eq(MnjxNmEi::getPnrNmId, pnrNmId)
                .like(MnjxNmEi::getEiInfo, "IN/")
                .list();
        deleteList.addAll(infantEiList);

        // 删除OSI YY
        deleteList.addAll(iMnjxNmOsiService.lambdaQuery()
                .eq(MnjxNmOsi::getPnrNmId, pnrNmId)
                .like(MnjxNmOsi::getPnrOsiInfo, "OSI YY 1INF")
                .list());
    }

    /**
     * 处理PNR级别运价删除
     */
    private void processPnrLevelFareRemoval(MnjxPnr pnr, List<Object> deleteList) {
        // 检查是否需要删除PNR级别的婴儿运价
        this.checkAndRemovePnrInfantFares(pnr, deleteList);

        // 检查是否需要删除PNR级别的儿童运价
        this.checkAndRemovePnrChildFares(pnr, deleteList);

        // 检查是否需要删除PNR级别的成人运价
        this.checkAndRemovePnrAdultFares(pnr, deleteList);
    }

    /**
     * 检查并删除PNR级别的婴儿运价
     */
    private void checkAndRemovePnrInfantFares(MnjxPnr pnr, List<Object> deleteList) {
        // 查询所有成人旅客的ID
        List<String> adultPnrNmIds = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list()
                .stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(adultPnrNmIds)) {
            return;
        }

        // 从deleteList中找出本次删除是否删除了婴儿，并获取本次删除婴儿的xnId
        List<String> infantXnIds = deleteList.stream()
                .filter(obj -> obj instanceof MnjxNmXn)
                .map(obj -> (MnjxNmXn) obj)
                .map(MnjxNmXn::getNmXnId)
                .collect(Collectors.toList());

        // 没有删除婴儿就不用去检查婴儿PNR运价了
        if (CollUtil.isEmpty(infantXnIds)) {
            return;
        }

        // 检查是否还有婴儿
        long remainingInfants = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, adultPnrNmIds)
                .notIn(MnjxNmXn::getNmXnId, infantXnIds)
                .count();

        if (remainingInfants == 0) {
            // 删除PNR级别的婴儿运价
            List<MnjxPnrFp> pnrInfantFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFp::getIsBaby, 1)
                    .list();
            deleteList.addAll(pnrInfantFpList);

            List<MnjxPnrFc> pnrInfantFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFc::getIsBaby, 1)
                    .list();
            deleteList.addAll(pnrInfantFcList);

            List<MnjxPnrFn> pnrInfantFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, 1)
                    .list();
            deleteList.addAll(pnrInfantFnList);

            List<MnjxPnrEi> pnrInfantEiList = iMnjxPnrEiService.lambdaQuery()
                    .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                    .like(MnjxPnrEi::getInputValue, "IN/")
                    .list();
            deleteList.addAll(pnrInfantEiList);
        }
    }

    /**
     * 检查并删除PNR级别的儿童运价
     */
    private void checkAndRemovePnrChildFares(MnjxPnr pnr, List<Object> deleteList) {
        // 从deleteList中找出本次删除是否删除了儿童，通过SSR CHLD获取，并获取本次删除儿童的pnrNmId
        List<String> childPnrNmIds = deleteList.stream()
                .filter(obj -> obj instanceof MnjxNmSsr)
                .map(obj -> (MnjxNmSsr) obj)
                .filter(ssr -> "CHLD".equals(ssr.getSsrType()))
                .map(MnjxNmSsr::getPnrNmId)
                .collect(Collectors.toList());

        // 没有删除儿童不检查PNR儿童运价
        if (CollUtil.isEmpty(childPnrNmIds)) {
            return;
        }

        // 检查是否还有儿童旅客
        long remainingChildren = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .notIn(MnjxPnrNm::getPnrNmId, childPnrNmIds)
                .count();

        if (remainingChildren == 0) {
            // 删除PNR级别的儿童运价
            List<MnjxPnrFp> pnrChildFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFp::getPatType, "CH")
                    .list();
            deleteList.addAll(pnrChildFpList);

            List<MnjxPnrFc> pnrChildFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFc::getPatType, "CH")
                    .list();
            deleteList.addAll(pnrChildFcList);

            List<MnjxPnrFn> pnrChildFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getPatType, "CH")
                    .list();
            deleteList.addAll(pnrChildFnList);
        }
    }

    /**
     * 检查并删除PNR级别的成人运价
     */
    private void checkAndRemovePnrAdultFares(MnjxPnr pnr, List<Object> deleteList) {
        // 从deleteList中找出本次删除旅客的pnrNmId
        List<String> pnrNmIds = deleteList.stream()
                .filter(obj -> obj instanceof MnjxPnrNm)
                .map(obj -> (MnjxPnrNm) obj)
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        // 没有旅客id说明没删除非婴儿旅客，不用查询PNR成人运价
        if (CollUtil.isEmpty(pnrNmIds)) {
            return;
        }

        // 查询PNR中是否有儿童旅客
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        List<MnjxNmSsr> chldSsrList = iMnjxNmSsrService.lambdaQuery()
                .in(MnjxNmSsr::getPnrNmId, pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                .eq(MnjxNmSsr::getSsrType, "CHLD")
                .list();
        if (CollUtil.isNotEmpty(chldSsrList)) {
            pnrNmIds.addAll(chldSsrList.stream().map(MnjxNmSsr::getPnrNmId).collect(Collectors.toList()));
        }

        // 检查是否还有成人旅客
        long remainingAdults = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .notIn(MnjxPnrNm::getPnrNmId, pnrNmIds)
                .count();

        if (remainingAdults == 0) {
            // 删除PNR级别的成人运价
            List<MnjxPnrFp> pnrAdultFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFp::getPatType, "AD")
                    .list();
            deleteList.addAll(pnrAdultFpList);

            List<MnjxPnrFc> pnrAdultFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFc::getPatType, "AD")
                    .list();
            deleteList.addAll(pnrAdultFcList);

            List<MnjxPnrFn> pnrAdultFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getPatType, "AD")
                    .list();
            deleteList.addAll(pnrAdultFnList);
        }
    }

    /**
     * 更新相关的PNR记录
     */
    private void updateRelatedPnrRecords(List<MnjxPnrRecord> dbRecordList, List<Object> deleteList, List<Object> updateList) {
        for (Object deleteItem : deleteList) {
            // 根据删除项查询对应的PNR记录
            List<MnjxPnrRecord> relatedRecords = this.findRelatedPnrRecords(dbRecordList, deleteItem);
            for (MnjxPnrRecord record : relatedRecords) {
                record.setChangeMark("X");
                updateList.add(record);
            }
        }
    }

    /**
     * 查找相关的PNR记录
     */
    private List<MnjxPnrRecord> findRelatedPnrRecords(List<MnjxPnrRecord> dbRecordList, Object deleteItem) {
        List<MnjxPnrRecord> records = new ArrayList<>();

        if (deleteItem instanceof MnjxPnrNm) {
            MnjxPnrNm item = (MnjxPnrNm) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmXn) {
            MnjxNmXn item = (MnjxNmXn) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "XN".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmSsr) {
            MnjxNmSsr item = (MnjxNmSsr) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "SSR".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmOsi) {
            MnjxNmOsi item = (MnjxNmOsi) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM OSI".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmRmk) {
            MnjxNmRmk item = (MnjxNmRmk) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM RMK".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmTc) {
            MnjxNmTc item = (MnjxNmTc) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM TC".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmOi) {
            MnjxNmOi item = (MnjxNmOi) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM OI".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmCt) {
            MnjxNmCt item = (MnjxNmCt) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM CT".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmEi) {
            MnjxNmEi item = (MnjxNmEi) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM EI".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmFc) {
            MnjxNmFc item = (MnjxNmFc) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM FC".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmFp) {
            MnjxNmFp item = (MnjxNmFp) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM FP".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxNmFn) {
            MnjxNmFn item = (MnjxNmFn) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "NM FN".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxPnrFc) {
            MnjxPnrFc item = (MnjxPnrFc) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "FC".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxPnrFp) {
            MnjxPnrFp item = (MnjxPnrFp) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "FP".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxPnrFn) {
            MnjxPnrFn item = (MnjxPnrFn) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "FN".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        } else if (deleteItem instanceof MnjxPnrEi) {
            MnjxPnrEi item = (MnjxPnrEi) deleteItem;
            records.addAll(dbRecordList.stream()
                    .filter(r -> "EI".equals(r.getPnrType()) && r.getPnrIndex().intValue() == item.getPnrIndex())
                    .collect(Collectors.toList()));
        }

        return records;
    }

    /**
     * 设置更新项的changeAtNo
     */
    private void setChangeAtNoForUpdates(List<Object> updateList, String newAtNo) {
        updateList.stream()
                .filter(u -> u instanceof MnjxPnrRecord)
                .map(u -> (MnjxPnrRecord) u)
                .filter(r -> StrUtil.isNotEmpty(r.getChangeMark()))
                .forEach(r -> r.setChangeAtNo(newAtNo));
    }

    /**
     * 批量执行删除和更新操作
     */
    private void batchExecuteOperations(List<Object> deleteList, List<Object> updateList) {
        // 批量删除
        if (CollUtil.isNotEmpty(deleteList)) {
            this.batchDelete(deleteList);
        }

        // 批量更新
        if (CollUtil.isNotEmpty(updateList)) {
            this.batchUpdate(updateList);
        }
    }

    /**
     * 批量删除
     */
    private void batchDelete(List<Object> deleteList) {
        // 按类型分组删除
        List<MnjxPnrNm> pnrNmList = new ArrayList<>();
        List<MnjxNmXn> nmXnList = new ArrayList<>();
        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        List<MnjxNmCt> nmCtList = new ArrayList<>();
        List<MnjxNmOsi> nmOsiList = new ArrayList<>();
        List<MnjxNmRmk> nmRmkList = new ArrayList<>();
        List<MnjxNmOi> nmOiList = new ArrayList<>();
        List<MnjxNmTc> nmTcList = new ArrayList<>();
        List<MnjxNmFp> nmFpList = new ArrayList<>();
        List<MnjxNmFc> nmFcList = new ArrayList<>();
        List<MnjxNmFn> nmFnList = new ArrayList<>();
        List<MnjxNmEi> nmEiList = new ArrayList<>();
        List<MnjxPnrFp> pnrFpList = new ArrayList<>();
        List<MnjxPnrFc> pnrFcList = new ArrayList<>();
        List<MnjxPnrFn> pnrFnList = new ArrayList<>();
        List<MnjxPnrEi> pnrEiList = new ArrayList<>();

        // 分类收集
        for (Object item : deleteList) {
            if (item instanceof MnjxPnrNm) {
                pnrNmList.add((MnjxPnrNm) item);
            } else if (item instanceof MnjxNmXn) {
                nmXnList.add((MnjxNmXn) item);
            } else if (item instanceof MnjxNmSsr) {
                nmSsrList.add((MnjxNmSsr) item);
            } else if (item instanceof MnjxNmCt) {
                nmCtList.add((MnjxNmCt) item);
            } else if (item instanceof MnjxNmOsi) {
                nmOsiList.add((MnjxNmOsi) item);
            } else if (item instanceof MnjxNmRmk) {
                nmRmkList.add((MnjxNmRmk) item);
            } else if (item instanceof MnjxNmOi) {
                nmOiList.add((MnjxNmOi) item);
            } else if (item instanceof MnjxNmTc) {
                nmTcList.add((MnjxNmTc) item);
            } else if (item instanceof MnjxNmFp) {
                nmFpList.add((MnjxNmFp) item);
            } else if (item instanceof MnjxNmFc) {
                nmFcList.add((MnjxNmFc) item);
            } else if (item instanceof MnjxNmFn) {
                nmFnList.add((MnjxNmFn) item);
            } else if (item instanceof MnjxNmEi) {
                nmEiList.add((MnjxNmEi) item);
            } else if (item instanceof MnjxPnrFp) {
                pnrFpList.add((MnjxPnrFp) item);
            } else if (item instanceof MnjxPnrFc) {
                pnrFcList.add((MnjxPnrFc) item);
            } else if (item instanceof MnjxPnrFn) {
                pnrFnList.add((MnjxPnrFn) item);
            } else if (item instanceof MnjxPnrEi) {
                pnrEiList.add((MnjxPnrEi) item);
            }
        }

        // 执行批量删除
        if (CollUtil.isNotEmpty(nmXnList)) {
            iMnjxNmXnService.removeByIds(nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmSsrList)) {
            iMnjxNmSsrService.removeByIds(nmSsrList.stream().map(MnjxNmSsr::getNmSsrId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmCtList)) {
            iMnjxNmCtService.removeByIds(nmCtList.stream().map(MnjxNmCt::getPnrCtId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmOsiList)) {
            iMnjxNmOsiService.removeByIds(nmOsiList.stream().map(MnjxNmOsi::getPnrOsiId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmRmkList)) {
            iMnjxNmRmkService.removeByIds(nmRmkList.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmOiList)) {
            iMnjxNmOiService.removeByIds(nmOiList.stream().map(MnjxNmOi::getNmOiId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmTcList)) {
            iMnjxNmTcService.removeByIds(nmTcList.stream().map(MnjxNmTc::getNmTcId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmFpList)) {
            iMnjxNmFpService.removeByIds(nmFpList.stream().map(MnjxNmFp::getNmFpId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmFcList)) {
            iMnjxNmFcService.removeByIds(nmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmFnList)) {
            iMnjxNmFnService.removeByIds(nmFnList.stream().map(MnjxNmFn::getNmFnId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmEiList)) {
            iMnjxNmEiService.removeByIds(nmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrFpList)) {
            iMnjxPnrFpService.removeByIds(pnrFpList.stream().map(MnjxPnrFp::getPnrFpId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrFcList)) {
            iMnjxPnrFcService.removeByIds(pnrFcList.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrFnList)) {
            iMnjxPnrFnService.removeByIds(pnrFnList.stream().map(MnjxPnrFn::getPnrFnId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrEiList)) {
            iMnjxPnrEiService.removeByIds(pnrEiList.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrNmList)) {
            iMnjxPnrNmService.removeByIds(pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()));
        }

        log.info("批量删除完成，共删除 {} 条记录", deleteList.size());
    }

    /**
     * 批量更新
     */
    private void batchUpdate(List<Object> updateList) {
        List<MnjxPnrRecord> pnrRecordList = updateList.stream()
                .filter(u -> u instanceof MnjxPnrRecord)
                .map(u -> (MnjxPnrRecord) u)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(pnrRecordList)) {
            iMnjxPnrRecordService.updateBatchById(pnrRecordList);
            log.info("批量更新Record完成，共更新 {} 条Record记录", pnrRecordList.size());
        }

        List<MnjxPnrNm> pnrNmList = updateList.stream()
                .filter(u -> u instanceof MnjxPnrNm)
                .map(u -> (MnjxPnrNm) u)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(pnrNmList)) {
            List<MnjxPnrNm> dbPnrNmList = iMnjxPnrNmService.listByIds(pnrNmList);
            if (CollUtil.isNotEmpty(dbPnrNmList)) {
                iMnjxPnrNmService.updateBatchById(pnrNmList);
                log.info("批量更新Nm完成，共更新 {} 条Nm记录", pnrNmList.size());
            }
        }

        List<MnjxPnrSeg> pnrSegList = updateList.stream()
                .filter(u -> u instanceof MnjxPnrSeg)
                .map(u -> (MnjxPnrSeg) u)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(pnrSegList)) {
            iMnjxPnrSegService.updateBatchById(pnrSegList);
            log.info("批量更新Nm完成，共更新 {} 条Nm记录", pnrSegList.size());
        }
    }

    /**
     * 重新排序和生成封口记录
     */
    private void reorderAndSeal(MnjxPnr pnr, String envelopType, String newAtNo) throws SguiResultException {
        // 重新排序所有项的pnr_index
        List<MnjxPnrRecord> recordList = new ArrayList<>();
        iUpdatePnrService.reorderAllPnrIndexesAndUpdate(pnr, recordList);

        // 生成封口记录
        this.generateSealingRecord(pnr, recordList, envelopType, newAtNo);
    }

    /**
     * 生成封口记录
     */
    private void generateSealingRecord(MnjxPnr pnr, List<MnjxPnrRecord> recordList, String envelopType, String newAtNo) throws SguiResultException {
        // 创建封口记录
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(newAtNo);
        pnrAt.setAtDateTime(new Date());

        // 设置封口类型
        if (StrUtil.isNotEmpty(envelopType)) {
            if ("I".equals(envelopType)) {
                pnrAt.setAtType("I");
            } else if ("KI".equals(envelopType)) {
                pnrAt.setAtType("KI");
            }
        }

        // 获取当前用户信息并设置
        try {
            pnrAt.setAtSiId(iSguiCommonService.getCurrentUserInfo().getSiId());
        } catch (Exception e) {
            log.warn("获取当前用户信息失败", e);
        }

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);

        // 历史记录，先删除以前changeMark为空的，再批量添加这次封口所有已排好序的记录
        iMnjxPnrRecordService.lambdaUpdate()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .remove();
        iMnjxPnrRecordService.saveBatch(recordList);
    }
}
